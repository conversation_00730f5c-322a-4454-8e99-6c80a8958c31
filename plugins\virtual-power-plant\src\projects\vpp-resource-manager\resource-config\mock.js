// mock.js - 资源配置页面 mock 数据

export const statsData = {
  user: 12,
  resource: 8,
  site: 20,
  device: 36
};

export const tableColumns = [
  { prop: "index", label: "序号", width: 60, filters: [] },
  { prop: "name", label: "用户名称", filters: [] },
  { prop: "area", label: "区域", filters: [] },
  { prop: "count", label: "资源数量（个）", filters: [] },
  { prop: "status", label: "状态", width: 100, filters: [] },
  { prop: "actions", label: "操作", width: 180, filters: [] }
];

export const tableData = [
  {
    index: 1,
    name: "地铁物业管理发展有限公司",
    area: "广州",
    count: 2,
    status: "正常"
  },
  {
    index: 2,
    name: "南方电网能源公司",
    area: "深圳",
    count: 5,
    status: "异常"
  },
  {
    index: 3,
    name: "珠江电力科技有限公司",
    area: "佛山",
    count: 1,
    status: "正常"
  },
  {
    index: 4,
    name: "广州市能源集团",
    area: "广州",
    count: 8,
    status: "维护中"
  },
  { index: 5, name: "广州地铁集团", area: "广州", count: 0, status: "无资源" },
  { index: 6, name: "测试用户A", area: "东莞", count: 3, status: "正常" },
  { index: 7, name: "测试用户B", area: "中山", count: 6, status: "异常" },
  { index: 8, name: "测试用户C", area: "珠海", count: 11, status: "正常" }
];

export const mockUser = {
  avatar: "https://randomuser.me/api/portraits/men/32.jpg",
  name: "张三",
  role: "虚拟电厂用户",
  deviceCount: 12,
  lastActive: "2024-07-07 10:30"
};

export const mockVppNode = {
  id: 1,
  name: "虚拟电厂A",
  type: "vpp"
};

export const treeData = [
  {
    id: 1,
    label: '虚拟电厂A',
    type: 'vpp',
    children: [
      {
        id: 11,
        label: '用户1',
        type: 'user',
        children: [
          {
            id: 111,
            label: '资源1',
            type: 'resource',
            children: [
              {
                id: 1111,
                label: '站点1',
                type: 'site',
                children: [
                  {
                    id: 11111,
                    label: '设备1',
                    type: 'device'
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        id: 12,
        label: '用户2',
        type: 'user'
      }
    ]
  },
  {
    id: 2,
    label: '虚拟电厂B',
    type: 'vpp'
  }
];
