<template>
  <div class="device-management">
    <CetButton type="primary" @click="handleAdd">新建设备</CetButton>
    <CetTable :data="devices" :columns="columns" />
  </div>
</template>
<script>
import { mapState, mapActions } from "vuex";
export default {
  name: "DeviceManagement",
  computed: {
    ...mapState("vppResource", ["devices"])
  },
  data() {
    return {
      columns: [
        { prop: "device_name", label: "设备名称" },
        { prop: "device_type", label: "设备类型" },
        { prop: "device_status", label: "设备状态" },
        { prop: "rated_power", label: "额定功率" },
        { prop: "action", label: "操作", slot: "action" }
      ]
    };
  },
  methods: {
    ...mapActions("vppResource", ["fetchDevices"]),
    handleAdd() {
      // TODO: 新建设备逻辑
    }
  },
  mounted() {
    this.fetchDevices();
  }
};
</script>
<style scoped>
.device-management {
  padding: 16px;
}
</style>
