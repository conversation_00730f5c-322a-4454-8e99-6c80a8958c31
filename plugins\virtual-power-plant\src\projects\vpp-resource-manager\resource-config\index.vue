<template>
  <div class="resource-config-page">
    <div class="main-content">
      <VppTree :treeData="treeData" @select="onSelectNode" />
      <div class="main-panel">
        <component :is="currentComponent" :node="selectedNode" />
      </div>
    </div>
  </div>
</template>

<script>
import VppTree from "./components/VppTree.vue";
import VppManagement from "./components/VppManagement.vue";
import UserManagement from "./components/UserManagement.vue";
import ResourceManagement from "./components/ResourceManagement.vue";
import SiteManagement from "./components/SiteManagement.vue";
import DeviceManagement from "./components/DeviceManagement.vue";
import { treeData as mockTreeData } from "./mock.js";

export default {
  name: "ResourceConfig",
  components: {
    VppTree,
    VppManagement,
    UserManagement,
    ResourceManagement,
    SiteManagement,
    DeviceManagement
  },
  data() {
    return {
      selectedNode: null,
      treeData: []
    };
  },
  computed: {
    currentComponent() {
      if (!this.selectedNode) return "VppManagement";
      switch (this.selectedNode.type) {
        case "vpp":
          return "VppManagement";
        case "user":
          return "UserManagement";
        case "resource":
          return "ResourceManagement";
        case "site":
          return "SiteManagement";
        case "device":
          return "DeviceManagement";
        default:
          return "VppManagement";
      }
    }
  },
  methods: {
    onSelectNode(node) {
      this.selectedNode = node;
    }
  },
  mounted() {
    // mock异步获取treeData
    setTimeout(() => {
      this.treeData = mockTreeData;
    }, 400);
  }
};
</script>

<style scoped>
.resource-config-page {
  padding: 24px;
  height: 100%;
}
.main-content {
  display: flex;
  margin-top: 16px;
  background: #f5f7fa;
  height: 100%;
}
.main-panel {
  flex: 1;
  padding: 0 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
