/**
 * 机组类型枚举定义
 *
 * 功能说明：
 * - 0: 全部, 1: 需求响应, 2: 调峰, 3: 调频, 4: 现货, 5: 中长期
 */

// 所有机组类型选项（包含"全部"）
export const UNIT_TYPE_OPTIONS = [
  { label: '全部', value: 0 },
  { label: '需求响应机组', value: 1 },
  { label: '调峰机组', value: 2 },
  { label: '调频机组', value: 3 },
  { label: '现货机组', value: 4 },
  { label: '中长期机组', value: 5 }
];

// 机组类型提示信息映射
export const UNIT_TYPE_MESSAGES = {
  1: '需求响应机组一般以资源所在地同一个地市聚合',
  2: '调峰机组一般以同一个并网节点聚合',
  3: '调频机组只能绑定直控型的资源，一般以同一个并网节点聚合',
  4: '单个机组所有聚合资源须位于同一现货市场出清节点（220千伏及以上电压等级母线）',
  5: '所有负荷类虚拟电厂现货交易机组合并为一个交易机组（"负荷类中长期交易机组"），其所有发电类虚拟电厂现货交易机组合并为另一个交易机组（"发电类中长期交易机组"）'
};
