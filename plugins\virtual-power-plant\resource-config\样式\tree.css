.figma-tree-container {
  width: 312px;
  background: #fff;
  border-radius: 4px;
  padding: 16px 0 0 0;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
}
.figma-search-bar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  width: 280px;
  margin: 24px 0 16px 16px;
  background: #fff;
}
.figma-search-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 14px;
  color: #424e5f;
  background: transparent;
}
.figma-search-icon {
  width: 16px;
  height: 16px;
  background: url('data:image/svg+xml;utf8,<svg fill="%23798492" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M11.742 10.344h-.797l-.282-.27a6.471 6.471 0 001.57-4.243A6.5 6.5 0 105.5 11.5c1.61 0 3.09-.59 4.243-1.57l.27.282v.797l4.25 4.25a1 1 0 001.415-1.415l-4.25-4.25zm-6.242 0A4.5 4.5 0 1110 5.5a4.5 4.5 0 01-4.5 4.5z"/></svg>')
    no-repeat center/contain;
}
.figma-tree {
  list-style: none;
  margin: 0 0 0 16px;
  padding: 0;
  width: 280px;
}
.figma-tree-item {
  background: #f6f8fa;
  border-radius: 4px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  padding: 4px 8px;
  gap: 8px;
  font-size: 14px;
  color: #424e5f;
}
.figma-tree-item-left {
  display: flex;
  align-items: center;
  gap: 8px;
}
.figma-caret {
  width: 12px;
  height: 12px;
  display: inline-block;
  background-repeat: no-repeat;
  background-position: center;
}
.figma-caret-right {
  background-image: url('data:image/svg+xml;utf8,<svg fill="%23A4ADB8" viewBox="0 0 12 12" xmlns="http://www.w3.org/2000/svg"><path d="M4 2l4 4-4 4"/></svg>');
}
.figma-caret-bottom {
  background-image: url('data:image/svg+xml;utf8,<svg fill="%23A4ADB8" viewBox="0 0 12 12" xmlns="http://www.w3.org/2000/svg"><path d="M2 4l4 4 4-4"/></svg>');
}
.figma-tree-label {
  font-size: 14px;
  color: #424e5f;
  line-height: 1.57em;
}
.figma-tree-icon {
  width: 16px;
  height: 16px;
  background: url('data:image/svg+xml;utf8,<svg fill="%23C9CDD4" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><circle cx="8" cy="8" r="7"/></svg>')
    no-repeat center/contain;
}
.figma-tree-sub {
  list-style: none;
  margin: 0 0 0 24px;
  padding: 0;
}
