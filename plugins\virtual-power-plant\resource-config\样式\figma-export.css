body {
  background: #fff;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  color: #13171f;
  margin: 0;
}
.figma-frame {
  min-height: 100vh;
  box-sizing: border-box;
}
.layout_ZTN9TS {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.layout_5QDTGZ {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px 24px;
}
.layout_XRSZS6 {
  display: flex;
  flex-direction: row;
  gap: 8px;
}
.layout_YRSBJB {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 8px 16px;
  background: #f5f7fa;
}
.layout_QC5VCB {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 48px;
}
.style_1J5MC8 {
  font-size: 12px;
  font-weight: 400;
  color: #798492;
  line-height: 1.33;
}
.style_0BET0Q {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.14;
}
.fill_CDWB17 {
  color: #4080ff;
}
.fill_PNOL9J {
  color: #ffc105;
}
.fill_ZSKHMJ {
  color: #8d7bfe;
}
.fill_N5FIWK {
  color: #6fbe0b;
}
.layout_ZS66RQ {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 24px;
}
.layout_WG65MO {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.layout_G81I23 {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}
.layout_MGZWI6 {
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-items: center;
}
.layout_QJA8O1 {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  padding: 1px 1px 1px 8px;
}
.layout_LUBK8D {
  width: 16px;
  height: 16px;
  display: inline-block;
}
.style_RLYY96 {
  font-size: 14px;
  font-weight: 400;
  color: #c9cdd4;
  text-align: center;
  line-height: 1.3;
}
.layout_JSSUGE {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  padding: 0 16px;
  width: 240px;
  height: 32px;
}
.style_E7INYV {
  font-size: 12px;
  font-weight: 400;
  color: #00b45e;
}
.style_Y6TINH {
  font-size: 14px;
  font-weight: 400;
  color: #13171f;
}
.fill_WGGE3L {
  color: #13171f;
}
.fill_4X1U40 {
  color: #a4adb8;
}
.layout_FHOXHY {
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-items: center;
}
.style_TUCJE9 {
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  line-height: 1.43;
}
.fill_PSPH3C {
  color: #424e5f;
}
.btn-outline {
  background: #fff;
  color: #424e5f;
  border: 1px solid #dcdfe6;
  border-radius: 3px;
  padding: 0 16px;
  height: 32px;
  cursor: pointer;
}
.btn-primary {
  background: #00b45e;
  color: #fff;
  border: none;
  border-radius: 3px;
  padding: 5px 16px;
  height: 32px;
  cursor: pointer;
}
.style_A9J3S0 {
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  line-height: 1.57;
}
.fill_IDHZMI {
  color: #fff;
}
.layout_X7Y79P {
  display: flex;
  flex-direction: row;
}
.layout_7X3Q7K,
.layout_E1FX3K,
.layout_B7JKQI {
  display: flex;
  flex-direction: column;
}
.fill_AJBBHU {
  background: #fafcff;
}
.layout_QMINX4 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  padding: 10px 24px;
  border-bottom: 1px solid #dcdfe6;
}
.style_RRSKOW {
  font-size: 12px;
  font-weight: 400;
  line-height: 1.5;
}
.layout_4W5AER {
  display: flex;
  flex-direction: row;
  gap: 24px;
  align-items: center;
  padding: 10px 24px;
  height: 40px;
}
.style_85D6UV {
  font-size: 14px;
  font-weight: 400;
}
.fill_HLGXQH {
  color: #f53f3f;
}
.layout_UESO5U {
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-items: center;
  justify-content: flex-end;
}
.layout_BTJIBS {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  padding: 6px 8px;
  border-radius: 3px;
  border: 1px solid #f0f2f5;
}
.pagination-btn {
  background: #fff;
  border: 1px solid #f0f2f5;
  border-radius: 3px;
  padding: 0 8px;
  height: 32px;
  margin: 0 2px;
  cursor: pointer;
}
.pagination-ellipsis {
  color: #c9cdd4;
  margin: 0 4px;
}
.pagination-input {
  width: 48px;
  height: 32px;
  border: 1px solid #f0f2f5;
  border-radius: 3px;
  text-align: center;
}
