<template>
  <div class="user-management">
    <CetButton type="primary" @click="handleAdd">新建用户</CetButton>
    <CetTable :data="users" :columns="columns" />
  </div>
</template>
<script>
import { mapState, mapActions } from "vuex";
export default {
  name: "UserManagement",
  computed: {
    ...mapState("vppResource", ["users"])
  },
  data() {
    return {
      columns: [
        { prop: "user_name", label: "用户名称" },
        { prop: "region", label: "区域" },
        { prop: "resource_count", label: "资源数" },
        { prop: "action", label: "操作", slot: "action" }
      ]
    };
  },
  methods: {
    ...mapActions("vppResource", ["fetchUsers"]),
    handleAdd() {
      // TODO: 新建用户逻辑
    }
  },
  mounted() {
    this.fetchUsers();
  }
};
</script>
<style scoped>
.user-management {
  padding: 16px;
}
</style>
