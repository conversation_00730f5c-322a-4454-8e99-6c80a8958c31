<template>
  <div class="vpp-management">
    <!-- 统计卡片区 -->
    <div class="stat-cards">
      <div class="stat-card" v-for="card in statCards" :key="card.label">
        <div class="stat-title">{{ card.label }}</div>
        <div class="stat-value" :class="card.colorClass">{{ card.value }}</div>
      </div>
    </div>
    <!-- 搜索与操作区 -->
    <div class="search-ops">
      <div class="search-bar">
        <cet-input
          v-model="search.keyword"
          placeholder="请输入关键字"
          class="search-input"
          clearable
        />
        <cet-simple-select
          v-model="search.region"
          :options="regionOptions"
          placeholder="请选择区域"
          class="region-select"
        />
        <div class="button-group">
          <cet-button class="btn-outline" @click="onImport">导入</cet-button>
          <cet-button class="btn-outline" @click="onExport">导出</cet-button>
          <cet-button type="primary" @click="onAddUser">
            新增代理用户
          </cet-button>
        </div>
      </div>
    </div>
    <!-- 表格区 -->
    <div class="table-area">
      <cet-table :data="tableData" :columns="columns">
        <template v-slot:action="{ row }">
          <cet-button type="text" @click="onDetail(row)">详情</cet-button>
          <cet-button type="text" @click="onEdit(row)">编辑</cet-button>
          <cet-button type="text" class="danger" @click="onDelete(row)">
            删除
          </cet-button>
        </template>
      </cet-table>
    </div>
    <!-- 分页区 -->
    <div class="pagination-area">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        :page-size="pagination.size"
        :current-page="pagination.page"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: "VppManagement",
  data() {
    return {
      // 统计卡片区数据
      statCards: [],
      // 搜索区数据
      search: {
        keyword: "",
        region: ""
      },
      regionOptions: [
        { label: "全部", value: "" },
        { label: "广州", value: "广州" },
        { label: "深圳", value: "深圳" },
        { label: "佛山", value: "佛山" },
        { label: "东莞", value: "东莞" },
        { label: "中山", value: "中山" },
        { label: "珠海", value: "珠海" }
      ],
      // 表格区数据
      columns: [],
      tableData: [],
      // 分页区数据
      pagination: {
        total: 0,
        page: 1,
        size: 10
      },
      loading: false
    };
  },
  created() {
    this.fetchStats();
    this.fetchTable();
  },
  methods: {
    fetchStats() {
      // 模拟接口请求
      this.loading = true;
      setTimeout(() => {
        this.statCards = [
          {
            label: "用户（个）",
            value: statsData.user,
            colorClass: "stat-blue"
          },
          {
            label: "资源（个）",
            value: statsData.resource,
            colorClass: "stat-yellow"
          },
          {
            label: "站点（个）",
            value: statsData.site,
            colorClass: "stat-purple"
          },
          {
            label: "设备（个）",
            value: statsData.device,
            colorClass: "stat-green"
          }
        ];
        this.loading = false;
      }, 400);
    },
    fetchTable() {
      this.loading = true;
      setTimeout(() => {
        // 模拟分页和搜索
        let filtered = tableData.filter(item => {
          const matchKeyword = this.search.keyword
            ? item.name.includes(this.search.keyword)
            : true;
          const matchRegion = this.search.region
            ? item.area === this.search.region
            : true;
          return matchKeyword && matchRegion;
        });
        this.pagination.total = filtered.length;
        const start = (this.pagination.page - 1) * this.pagination.size;
        const end = start + this.pagination.size;
        this.tableData = filtered.slice(start, end);
        this.columns = [
          { prop: "index", label: "序号", width: 60 },
          { prop: "name", label: "用户名称" },
          { prop: "area", label: "区域" },
          { prop: "count", label: "资源数量（个）" },
          { prop: "status", label: "状态", width: 100 },
          { prop: "action", label: "操作", slot: "action", width: 180 }
        ];
        this.loading = false;
      }, 500);
    },
    onImport() {},
    onExport() {},
    onAddUser() {},
    onDetail(row) {},
    onEdit(row) {},
    onDelete(row) {},
    onPageSizeChange(size) {
      this.pagination.size = size;
      this.pagination.page = 1;
      this.fetchTable();
    },
    onPageChange(page) {
      this.pagination.page = page;
      this.fetchTable();
    }
  },
  watch: {
    "search.keyword": function () {
      this.pagination.page = 1;
      this.fetchTable();
    },
    "search.region": function () {
      this.pagination.page = 1;
      this.fetchTable();
    }
  }
};
</script>

<style scoped>
.vpp-management {
  padding: 24px;
  background: #fff;
  min-height: 100vh;
}
.stat-cards {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}
.stat-card {
  flex: 1;
  background: #f5f7fa;
  border-radius: 8px;
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-title {
  font-size: 12px;
  color: #798492;
  margin-bottom: 8px;
}
.stat-value {
  font-size: 20px;
  font-weight: 600;
}
.stat-blue {
  color: #4080ff;
}
.stat-yellow {
  color: #ffc105;
}
.stat-purple {
  color: #8d7bfe;
}
.stat-green {
  color: #6fbe0b;
}
.search-ops {
  margin-bottom: 16px;
}
.search-bar {
  display: flex;
  align-items: center;
  gap: 16px;
}
.search-input {
  width: 240px;
}
.region-select {
  width: 240px;
}
.button-group {
  display: flex;
  gap: 8px;
  margin-left: auto;
}
.btn-outline {
  background: #fff;
  color: #424e5f;
  border: 1px solid #dcdfe6;
  border-radius: 3px;
  padding: 0 16px;
  height: 32px;
  cursor: pointer;
}
.table-area {
  margin-bottom: 16px;
}
.danger {
  color: #f53f3f;
}
.pagination-area {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 8px;
}
</style>
