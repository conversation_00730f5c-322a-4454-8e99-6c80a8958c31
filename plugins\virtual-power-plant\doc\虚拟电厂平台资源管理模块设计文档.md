## 1. 概述

### 1.1 项目背景

虚拟电厂平台通过先进信息通信技术和软件系统，实现分布式电源、储能系统、可控负荷、电动汽车等分布式能源资源的聚合和协调优化，作为特殊电厂参与电力市场和电网运行。

### 1.2 模块功能

资源管理模块实现虚拟电厂、用户、资源、站点、设备的层级化管理，支持增删改查操作，并提供数据关联配置功能。

使用融合平台，资源管理模块是一个插件：vpp-resource-manager

### 1.3 技术栈

- **后端**: JDK8 + Spring Boot 2.x +融合平台
- **前端**: Vue 2 + js+ Element  + vuex+css+融合平台
- **其他**: Redis、RabbitMQ、Docker
- **主备**：cluster-assistant
- **系统**：麒麟2303

## 2. 系统架构设计

### 2.1 整体架构

```mermaid
graph TB
    subgraph "前端层"
        A[Vue3 + TypeScript]
        B[Element Plus UI]
        C[Pinia 状态管理]
    end
    
    subgraph "网关层"
        D[Spring Cloud Gateway]
    end
    
    subgraph "应用层"
        E[Controller 控制层]
        F[Service 业务层]
    end
    
    subgraph "数据层"
        
    end
    

    
    A --> D
    D --> E
    E --> F
    F --> G
    F --> L
    G --> H
  
```

### 2.2 模块架构

```mermaid
graph LR
    subgraph "资源管理模块"
        A[虚拟电厂管理]
        B[用户管理]
        C[资源管理]
        D[站点管理]
        E[设备管理]
        F[数据关联]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
```

### 2.3 分层设计

| 层级   | 职责               | 主要组件                |
| ------ | ------------------ | ----------------------- |
| 表现层 | 用户交互、数据展示 | Vue组件、路由、状态管理 |
| 控制层 | 请求处理、参数校验 | Controller、拦截器      |
| 业务层 | 业务逻辑、事务管理 | Service、业务规则       |
| 数据层 | 数据访问、缓存管理 | Mapper、Redis、数据库   |

## 3. 数据模型设计

### 3.1 实体关系图

```mermaid
erDiagram
    VPP ||--o{ USER : contains
    USER ||--o{ RESOURCE : owns
    RESOURCE ||--o{ SITE : contains
    SITE ||--o{ DEVICE : contains
    DEVICE ||--o{ DATA_POINT : associates
    
    VPP {
        bigint id PK
        string vpp_name
        string province
        string vpp_type
        string description
        int user_count
        int resource_count
        decimal adjustable_capacity
        datetime create_time
        datetime update_time
        boolean deleted
    }
    
    USER {
        bigint id PK
        bigint vpp_id FK
        string user_id
        string user_name
        string user_type
        string region
        string contact_info
        int resource_count
        datetime create_time
        datetime update_time
        boolean deleted
    }
    
    RESOURCE {
        bigint id PK
        bigint user_id FK
        string resource_id
        string resource_name
        string resource_type
        string electricity_user_numbers
        decimal registered_capacity
        string resource_category
        string response_mode
        string resource_status
        boolean platform_direct_control
        string region
        decimal max_up_rate
        decimal max_down_rate
        int site_count
        datetime create_time
        datetime update_time
        boolean deleted
    }
    
    SITE {
        bigint id PK
        bigint resource_id FK
        string site_id
        string site_name
        string site_type
        string site_address
        decimal longitude
        decimal latitude
        int device_count
        datetime create_time
        datetime update_time
        boolean deleted
    }
    
    DEVICE {
        bigint id PK
        bigint site_id FK
        string device_id
        string device_name
        string device_type
        string device_sub_type
        string device_status
        string manufacturer
        string model
        decimal rated_power
        date installation_date
        datetime create_time
        datetime update_time
        boolean deleted
    }
    
    DATA_POINT {
        bigint id PK
        bigint device_id FK
        string point_id
        string point_name
        string point_type
        string data_type
        string unit
        boolean is_controllable
        datetime create_time
        datetime update_time
        boolean deleted
    }
```

### 3.2 核心实体设计

#### 3.2.1 虚拟电厂实体 (virtualpowerplant)

| 字段名              | 类型    | 长度 | 必填 | 描述                |
| ------------------- | ------- | ---- | ---- | ------------------- |
| id                  | bigint  | -    | 是   | 主键ID              |
| vpp_name            | varchar | 100  | 是   | 虚拟电厂名称        |
| province            | varchar | 50   | 是   | 所属省份            |
| vpp_type            | varchar | 20   | 是   | 类型(调节型/能量型) |
| description         | text    | -    | 否   | 描述信息            |
| user_count          | int4    | -    | 否   | 用户数量            |
| resource_count      | int4    | -    | 否   | 资源数量            |
| adjustable_capacity | decimal | 10,2 | 否   | 可调节容量          |
| picture             | varchar | 200  | 否   | 图片                |
| createtime          | int8    |      | 是   | 成立时间            |
| operatorcode        | varchar | 200  | 是   | 运营商编号          |
|                     | double  |      | 否   | 申报价格上限        |
|                     |         |      |      | 申报价格下限        |

**业务规则**：

- 虚拟电厂名称在系统内唯一
- 删除时需检查是否存在关联用户
- 自动统计下级用户和资源数量

#### 3.2.2 用户实体 (virtualpowerplantuser)

| 字段名         | 类型    | 长度 | 必填 | 描述       |
| -------------- | ------- | ---- | ---- | ---------- |
| id             | bigint  | -    | 是   | 主键ID     |
| vpp_id         | bigint  | -    | 是   | 虚拟电厂ID |
| phone_number   | int8    |      |      | 电话       |
| contact_person | varchar | 200  |      | 联系人     |
| user_name      | varchar | 100  | 是   | 用户名称   |
| address        | varchar | 250  | 是   | 地址       |
| region         | varchar | 100  | 否   | 所属区域   |
| resource_count | int     | -    | 否   | 资源数量   |
| city           | int8    |      | 是   | 所属市     |
| district       | int8    |      | 是   | 所属区     |

**业务规则**：

- 用户ID系统自动生成，保证唯一性
- 删除时需检查是否存在关联资源
- 自动统计下级资源数量
- 用户在一个场站下唯一

#### 3.2.3 资源实体 (virtualpowerplantresource)

| 字段名                   | 类型    | 长度 | 必填 | 描述                     |
| ------------------------ | ------- | ---- | ---- | ------------------------ |
| id                       | bigint  | -    | 是   | 主键ID                   |
| user_id                  | bigint  | -    | 是   | 用户ID                   |
| resource_name            | varchar | 100  | 是   | 资源名称                 |
| resource_type            | varchar | 20   | 是   | 资源类型(发电/储电/用电) |
| electricity_user_numbers | text    | -    | 否   | 用电户号                 |
| registered_capacity      | decimal | 10,2 | 否   | 报装容量                 |
| response_mode            | varchar | 20   | 否   | 响应方式(自动/手动)      |
| resource_status          | varchar | 20   | 是   | 资源状态                 |
| platform_direct_control  | boolean | -    | 否   | 平台直控                 |
| maximum_operable_power   | double  |      |      | 最大可运行功率           |
| minmum_operable_power    | double  |      |      | 最小可运行功率           |
| maximum_upscaling_rate   | double  |      |      | 最大上调速率             |
| Maximum_downward_rate    | double  |      |      | 最大下调速率             |
| longitude                | double  |      |      | 经度                     |
| latitude                 | double  |      |      | 纬度                     |
| contact_person           | varchar |      |      | 联系人                   |
| phone_number             | varchar |      |      | 联系电话                 |
| district                 | int4    |      |      | 区域                     |
| address                  | varhcar |      |      | 地址                     |
| site_count               | int     | -    | 否   | 站点数量                 |

**枚举定义**：

- 资源类型：发电资源、储电资源、用电资源
- 资源状态：投运、测试、退役、停机
- 响应方式：自动响应、手动响应
- 资源类别：

#### 3.2.4 站点实体 (virtualpowerplantsite)

| 字段名         | 类型    | 长度 | 必填 | 描述         |
| -------------- | ------- | ---- | ---- | ------------ |
| id             | bigint  | -    | 是   | 主键ID       |
| resource_id    | bigint  | -    | 是   | 资源ID       |
| longitude      | double  |      |      | 经度         |
| latitude       | double  |      |      | 纬度         |
| contact_person | varchar |      |      | 联系人       |
| phone_number   | varchar |      |      | 联系电话     |
| site_name      | varchar | 100  | 是   | 站点名称     |
| site_type      | varchar | 10   | 是   | 站点类型编码 |
| site_address   | varchar | 200  | 否   | 站点地址     |
| device_count   | int     | -    | 否   | 设备数量     |
| room_id        | int8    |      | 是   | 对应房间id   |

**站点类型编码**：

- 01: 自备电源
- 02: 用户侧储能
- 03: 电动汽车
- 04: 充电站
- 05: 换电站
- 06: 楼宇空调
- 07: 工商业可调节负荷
- 08: 分布式光伏
- 09: 分散式风电
- 10: 分布式独立储能
- 11: 非可调节负荷
- 99: 其他

#### 3.2.5 设备实体 (virtualpowerplantdevice)

| 字段名          | 类型    | 长度 | 必填 | 描述       |
| --------------- | ------- | ---- | ---- | ---------- |
| id              | int8    | -    | 是   | 主键ID     |
| site_id         | int8    | -    | 是   | 站点ID     |
| device_id       | varchar | 50   | 是   | 设备编号   |
| device_name     | varchar | 100  | 是   | 设备名称   |
| device_type     | varchar | 50   | 是   | 设备类型   |
| device_sub_type | varchar | 50   | 否   | 设备子类型 |
| device_status   | varchar | 20   | 是   | 设备状态   |
| rated_power     | decimal | 10,2 | 否   | 额定功率   |

**设备类型分类**：

- 储能设备：PCS变流器、DC变流器、BMS设备、EMS虚拟设备、储能集装箱
- 光伏设备：逆变器、直流汇流箱、气象仪
- 充电设备：直流充电桩、交流充电桩
- 空调设备：冷水机组、冷却泵、冷冻泵、冷却塔、集/分水器
- 配电设备：参照融合平台标准
- 用能设备：提升泵、鼓风机、多联机空调、空调主机、风机盘管等

**设备状态**：

- 无信息、离线故障、报警、正常运行、占用、空闲

  #### 3.2.6电厂设备和管网设备关联信息表

  | 字段          | 类型    | 描述         |
  | ------------- | ------- | ------------ |
  | id            |         |              |
  | vpp_device_id | int8    | 电厂设备id   |
  | objectlabel   | varchar | 管网设备类型 |
  | objectid      | int8    | 管网设备id   |

  

## 4. 接口设计

### 4.1 RESTful API 规范

#### 4.1.1 URL 设计规范

- 基础路径：`/api/v1`
- 资源命名：使用名词复数形式
- 层级关系：通过URL路径体现

#### 4.1.2 HTTP 方法规范

- GET：查询操作
- POST：创建操作
- PUT：完整更新操作
- PATCH：部分更新操作
- DELETE：删除操作

#### 4.1.3 响应格式规范

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2025-06-17T10:30:00"
}
```

### 4.2 核心接口设计

#### 4.2.1 虚拟电厂管理接口

| 方法   | 路径                         | 描述                 |
| ------ | ---------------------------- | -------------------- |
| POST   | /api/v1/vpp                  | 创建虚拟电厂         |
| GET    | /api/v1/vpp/{id}             | 获取虚拟电厂详情     |
| PUT    | /api/v1/vpp/{id}             | 更新虚拟电厂         |
| DELETE | /api/v1/vpp/{id}             | 删除虚拟电厂         |
| GET    | /api/v1/vpp                  | 分页查询虚拟电厂列表 |
| GET    | /api/v1/vpp/types/{province} | 获取省份类型配置     |

#### 4.2.2 用户管理接口

| 方法   | 路径                      | 描述                   |
| ------ | ------------------------- | ---------------------- |
| POST   | /api/v1/user              | 创建用户               |
| GET    | /api/v1/user/{id}         | 获取用户详情           |
| PUT    | /api/v1/user/{id}         | 更新用户               |
| DELETE | /api/v1/user/{id}         | 删除用户               |
| GET    | /api/v1/user              | 分页查询用户列表       |
| GET    | /api/v1/vpp/{vppId}/users | 获取虚拟电厂下用户列表 |

#### 4.2.3 资源管理接口

| 方法   | 路径                            | 描述               |
| ------ | ------------------------------- | ------------------ |
| POST   | /api/v1/resource                | 创建资源           |
| GET    | /api/v1/resource/{id}           | 获取资源详情       |
| PUT    | /api/v1/resource/{id}           | 更新资源           |
| DELETE | /api/v1/resource/{id}           | 删除资源           |
| GET    | /api/v1/resource                | 分页查询资源列表   |
| GET    | /api/v1/user/{userId}/resources | 获取用户下资源列表 |

#### 4.2.4 站点管理接口

| 方法   | 路径                                | 描述               |
| ------ | ----------------------------------- | ------------------ |
| POST   | /api/v1/site                        | 创建站点           |
| GET    | /api/v1/site/{id}                   | 获取站点详情       |
| PUT    | /api/v1/site/{id}                   | 更新站点           |
| DELETE | /api/v1/site/{id}                   | 删除站点           |
| GET    | /api/v1/site                        | 分页查询站点列表   |
| GET    | /api/v1/resource/{resourceId}/sites | 获取资源下站点列表 |

#### 4.2.5 设备管理接口

| 方法   | 路径                          | 描述               |
| ------ | ----------------------------- | ------------------ |
| POST   | /api/v1/device                | 创建设备           |
| GET    | /api/v1/device/{id}           | 获取设备详情       |
| PUT    | /api/v1/device/{id}           | 更新设备           |
| DELETE | /api/v1/device/{id}           | 删除设备           |
| GET    | /api/v1/device                | 分页查询设备列表   |
| GET    | /api/v1/site/{siteId}/devices | 获取站点下设备列表 |

#### 4.2.6 数据关联接口

| 方法   | 路径                                 | 描述           |
| ------ | ------------------------------------ | -------------- |
| POST   | /api/v1/device/{deviceId}/datapoints | 关联数据点     |
| GET    | /api/v1/device/{deviceId}/datapoints | 获取设备数据点 |
| PUT    | /api/v1/datapoint/{id}               | 更新数据点配置 |
| DELETE | /api/v1/datapoint/{id}               | 删除数据点关联 |

### 4.3 请求响应模型设计

#### 4.3.1 分页查询模型

```json
{
  "page": 1,
  "size": 20,
  "total": 100,
  "records": []
}
```

#### 4.3.2 虚拟电厂模型

```json
{
  "id": 1,
  "vppName": "湖北虚拟电厂",
  "province": "湖北省",
  "vppType": "调节型",
  "description": "描述信息",
  "userCount": 10,
  "resourceCount": 50,
  "adjustableCapacity": 1000.50
}
```

## 5. 前端设计

### 5.1 页面结构设计

```mermaid
graph TB
    A[资源管理主页] --> B[虚拟电厂管理]
    A --> C[用户管理]
    A --> D[资源配置]
    A --> E[站点管理]
    A --> F[设备管理]
    
    B --> B1[虚拟电厂列表]
    B --> B2[新建/编辑虚拟电厂]
    
    C --> C1[用户列表]
    C --> C2[新建/编辑用户]
    
    D --> D1[资源列表]
    D --> D2[资源配置表单]
    
    E --> E1[站点列表]
    E --> E2[站点配置表单]
    
    F --> F1[设备列表]
    F --> F2[设备配置表单]
    F --> F3[数据点关联]
```

### 5.2 组件设计

#### 5.2.1 业务组件

- **VppManagement**：虚拟电厂管理
- **UserManagement**：用户管理
- **ResourceConfig**：资源配置
- **SiteManagement**：站点管理
- **DeviceManagement**：设备管理
- **VppTree**:电厂树

### 5.3 状态管理设计

#### 5.3.1 Store结构

```
resourceStore/
├── state/
│   ├── VppTree          // 虚拟电厂树
├── getters/
│   ├── VppTree       // 虚拟电厂树
└── actions/
    ├── VppTree     // 虚拟电厂树
```

## 6. 关键业务流程

### 6.1 虚拟电厂创建流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant DB as 数据库
    participant R as Redis

    U->>F: 填写虚拟电厂信息
    F->>F: 前端表单验证
    F->>B: POST /api/v1/vpp
    B->>B: 参数校验
    B->>DB: 检查名称是否重复
    DB-->>B: 返回查询结果
    
    alt 名称不重复
        B->>DB: 插入虚拟电厂记录
        DB-->>B: 返回插入结果
        B->>R: 清除相关缓存
        B-->>F: 返回成功响应
        F-->>U: 显示创建成功
    else 名称重复
        B-->>F: 返回错误响应
        F-->>U: 显示错误信息
    end
```

### 6.2 层级数据管理流程

```mermaid
graph TD
    A[虚拟电厂] --> B[用户管理]
    B --> C[资源管理]
    C --> D[站点管理]
    D --> E[设备管理]
   
    
    G[统计数量] --> A
    G --> B
    G --> C
    G --> D
    
    H[删除检查] --> A
    H --> B
    H --> C
    H --> D
    
    subgraph "业务规则"
        I[只能删除无下级的节点]
        J[自动生成唯一ID]
        K[自动统计下级数量]
        L[支持批量操作]
    end
```

### 6.3 数据关联配置流程

### 

```mermaid
graph LR
    subgraph "虚拟电厂层级"
        A[虚拟电厂管理]
        B[用户管理]
        C[资源管理]
        D[站点管理]
        E[电厂设备]

    end
    subgraph "管网层级"
        G[房间]
        H[管网设备]
        I[监测设备]
    end
    G --> H
    H --> I
    E --> H
    A --> B
    B --> C
    C --> D
    D --> E

```

虚拟电厂层级的配置都是从做到右是1对多

站点管理需要关联一个房间，只是一个映射；在创建站点时，选择对应房间

一个电厂设备对应多个管网设备。在创建电厂设备时，选择多个管网设备

#### 供能关系配置：

管网设备对电厂设备供能

电厂设备对站点供能

站点对资源供能

资源对用户供能

用户对虚拟电厂供能

光伏，储能插件使用quantityaggregationdata



### 6.4 数据统计更新流程

```mermaid
flowchart LR
    A[数据变更事件] --> B{变更类型}
    
    B -->|新增| C[增加计数]
    B -->|删除| D[减少计数]
    B -->|修改| E[检查状态变化]
    
    C --> F[向上级传播]
    D --> F
    E --> F
    
    F --> G[更新父级统计]
    G --> H[触发缓存更新]
    H --> I[发送变更通知]
```

## 