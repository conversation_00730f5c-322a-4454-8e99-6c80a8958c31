<template>
  <div class="vpp-tree-layout">
    <div class="vpp-tree-container">
      <div class="vpp-search-bar">
        <input
          type="text"
          class="vpp-search-input"
          v-model="searchText"
          placeholder="请输入内容"
        />
        <span class="vpp-search-icon"></span>
      </div>
      <CetTree
        class="vpp-tree"
        :inputData_in="filteredTreeData"
        :props="treeProps"
        :highlight-current="true"
        node-key="id"
        :default-expand-all="true"
        @node-click="handleSelect"
        :render-content="renderNode"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: "VppTree",
  props: {
    treeData: { type: Array, default: () => [] }
  },
  data() {
    return {
      searchText: "",
      treeProps: { label: "label", children: "children" }
    };
  },
  computed: {
    filteredTreeData() {
      if (!this.searchText) return this.treeData;
      // 简单过滤，label包含搜索内容
      const filter = nodes => {
        return nodes
          .map(node => {
            const children = node.children ? filter(node.children) : [];
            if (
              node.label.includes(this.searchText) ||
              (children && children.length)
            ) {
              return { ...node, children };
            }
            return null;
          })
          .filter(Boolean);
      };
      return filter(this.treeData);
    }
  },
  methods: {
    handleSelect(node) {
      this.$emit("select", node);
    },
    renderNode(h, { node, data }) {
      return h("div", { class: "vpp-tree-item-left" }, [
        h("span", {
          class: [
            "vpp-caret",
            node.expanded ? "vpp-caret-bottom" : "vpp-caret-right"
          ]
        }),
        data.icon ? h("span", { class: "vpp-tree-icon" }) : null,
        h("span", { class: "vpp-tree-label" }, data.label)
      ]);
    }
  }
};
</script>

<style scoped>
.vpp-tree-layout {
  display: flex;
  width: 17%;
  height: 100%;
}
.vpp-tree-container {
  width: 100%;
  background: #fff;
  border-radius: 4px 0 0 4px;
  padding: 16px 0 0 0;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  border-right: 1px solid #e5e6eb;
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.vpp-search-bar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  width: 80%;
  margin: 24px 0 16px 16px;
  background: #fff;
}
.vpp-search-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 14px;
  color: #424e5f;
  background: transparent;
}
.vpp-search-icon {
  width: 16px;
  height: 16px;
  background: url('data:image/svg+xml;utf8,<svg fill="%23798492" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M11.742 10.344h-.797l-.282-.27a6.471 6.471 0 001.57-4.243A6.5 6.5 0 105.5 11.5c1.61 0 3.09-.59 4.243-1.57l.27.282v.797l4.25 4.25a1 1 0 001.415-1.415l-4.25-4.25zm-6.242 0A4.5 4.5 0 1110 5.5a4.5 4.5 0 01-4.5 4.5z"/></svg>')
    no-repeat center/contain;
}
.vpp-tree {
  flex: 1;
  list-style: none;
  margin: 0 0 0 16px;
  padding: 0;
  width: 280px;
  background: transparent;
}
.vpp-tree-item-left {
  display: flex;
  align-items: center;
  gap: 8px;
}
.vpp-caret {
  width: 12px;
  height: 12px;
  display: inline-block;
  background-repeat: no-repeat;
  background-position: center;
}
.vpp-caret-right {
  background-image: url('data:image/svg+xml;utf8,<svg fill="%23A4ADB8" viewBox="0 0 12 12" xmlns="http://www.w3.org/2000/svg"><path d="M4 2l4 4-4 4"/></svg>');
}
.vpp-caret-bottom {
  background-image: url('data:image/svg+xml;utf8,<svg fill="%23A4ADB8" viewBox="0 0 12 12" xmlns="http://www.w3.org/2000/svg"><path d="M2 4l4 4 4-4"/></svg>');
}
.vpp-tree-label {
  font-size: 14px;
  color: #424e5f;
  line-height: 1.57em;
}
.vpp-tree-icon {
  width: 16px;
  height: 16px;
  background: url('data:image/svg+xml;utf8,<svg fill="%23C9CDD4" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><circle cx="8" cy="8" r="7"/></svg>')
    no-repeat center/contain;
}
.vpp-content {
  flex: 1;
  background: #fff;
  border-radius: 0 4px 4px 0;
  height: 100%;
  box-sizing: border-box;
  padding: 24px;
  overflow-y: auto;
}
</style>
