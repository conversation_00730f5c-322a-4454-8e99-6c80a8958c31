<template>
  <div class="resource-management">
    <CetButton type="primary" @click="handleAdd">新建资源</CetButton>
    <CetTable :data="resources" :columns="columns" />
  </div>
</template>
<script>
import { mapState, mapActions } from "vuex";
export default {
  name: "ResourceManagement",
  computed: {
    ...mapState("vppResource", ["resources"])
  },
  data() {
    return {
      columns: [
        { prop: "resource_name", label: "资源名称" },
        { prop: "resource_type", label: "资源类型" },
        { prop: "registered_capacity", label: "报装容量" },
        { prop: "resource_status", label: "资源状态" },
        { prop: "site_count", label: "站点数" },
        { prop: "action", label: "操作", slot: "action" }
      ]
    };
  },
  methods: {
    ...mapActions("vppResource", ["fetchResources"]),
    handleAdd() {
      // TODO: 新建资源逻辑
    }
  },
  mounted() {
    this.fetchResources();
  }
};
</script>
<style scoped>
.resource-management {
  padding: 16px;
}
</style>
