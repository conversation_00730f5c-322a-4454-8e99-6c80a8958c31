import api from "@/api/vpp-resource";

const state = {
  vpps: [],
  users: [],
  resources: [],
  sites: [],
  devices: [],
  loading: false
};

const mutations = {
  SET_VPPS(state, vpps) {
    state.vpps = vpps;
  },
  SET_USERS(state, users) {
    state.users = users;
  },
  SET_RESOURCES(state, resources) {
    state.resources = resources;
  },
  SET_SITES(state, sites) {
    state.sites = sites;
  },
  SET_DEVICES(state, devices) {
    state.devices = devices;
  },
  SET_LOADING(state, loading) {
    state.loading = loading;
  }
};

const actions = {
  async fetchVpps({ commit }, params) {
    commit("SET_LOADING", true);
    const res = await api.listVpp(params);
    commit("SET_VPPS", res.data.records || []);
    commit("SET_LOADING", false);
  },
  async fetchUsers({ commit }, params) {
    commit("SET_LOADING", true);
    const res = await api.listUser(params);
    commit("SET_USERS", res.data.records || []);
    commit("SET_LOADING", false);
  },
  async fetchResources({ commit }, params) {
    commit("SET_LOADING", true);
    const res = await api.listResource(params);
    commit("SET_RESOURCES", res.data.records || []);
    commit("SET_LOADING", false);
  },
  async fetchSites({ commit }, params) {
    commit("SET_LOADING", true);
    const res = await api.listSite(params);
    commit("SET_SITES", res.data.records || []);
    commit("SET_LOADING", false);
  },
  async fetchDevices({ commit }, params) {
    commit("SET_LOADING", true);
    const res = await api.listDevice(params);
    commit("SET_DEVICES", res.data.records || []);
    commit("SET_LOADING", false);
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
