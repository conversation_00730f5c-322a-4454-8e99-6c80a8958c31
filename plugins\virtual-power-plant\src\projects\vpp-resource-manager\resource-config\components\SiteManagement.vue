<template>
  <div class="site-management">
    <CetButton type="primary" @click="handleAdd">新建站点</CetButton>
    <CetTable :data="sites" :columns="columns" />
  </div>
</template>
<script>
import { mapState, mapActions } from "vuex";
export default {
  name: "SiteManagement",
  computed: {
    ...mapState("vppResource", ["sites"])
  },
  data() {
    return {
      columns: [
        { prop: "site_name", label: "站点名称" },
        { prop: "site_type", label: "站点类型" },
        { prop: "site_address", label: "站点地址" },
        { prop: "device_count", label: "设备数" },
        { prop: "action", label: "操作", slot: "action" }
      ]
    };
  },
  methods: {
    ...mapActions("vppResource", ["fetchSites"]),
    handleAdd() {
      // TODO: 新建站点逻辑
    }
  },
  mounted() {
    this.fetchSites();
  }
};
</script>
<style scoped>
.site-management {
  padding: 16px;
}
</style>
